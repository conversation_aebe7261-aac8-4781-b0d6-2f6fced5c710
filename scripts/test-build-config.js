#!/usr/bin/env node

/**
 * 测试构建配置脚本
 * 验证不同环境下的配置是否正确
 */

const { getImageOptimizationConfig } = require('../build/image-optimization.config');

console.log('🧪 测试构建配置...\n');

// 测试不同环境的图片优化配置
const testEnvironments = [
  { NODE_ENV: 'development', NODE_ENV_TYPE: undefined },
  { NODE_ENV: 'production', NODE_ENV_TYPE: 'kktest' },
  { NODE_ENV: 'production', NODE_ENV_TYPE: 'ykytest' },
  { NODE_ENV: 'production', NODE_ENV_TYPE: 'kkprod' },
  { NODE_ENV: 'production', NODE_ENV_TYPE: 'ykyprod' },
];

testEnvironments.forEach(env => {
  // 设置环境变量
  process.env.NODE_ENV = env.NODE_ENV;
  process.env.NODE_ENV_TYPE = env.NODE_ENV_TYPE;
  
  console.log(`📋 环境: ${env.NODE_ENV} - ${env.NODE_ENV_TYPE || 'default'}`);
  
  try {
    const config = getImageOptimizationConfig();
    console.log(`   JPEG质量: ${config.mozjpeg.quality}`);
    console.log(`   PNG质量: ${JSON.stringify(config.pngquant.quality)}`);
    console.log(`   WebP质量: ${config.webp.quality}`);
    console.log(`   PNG优化: ${config.optipng.enabled ? '启用' : '禁用'}`);
    console.log('   ✅ 配置正常\n');
  } catch (error) {
    console.log(`   ❌ 配置错误: ${error.message}\n`);
  }
});

// 测试webpack配置加载
console.log('🔧 测试Webpack配置加载...');

try {
  // 重置环境变量
  process.env.NODE_ENV = 'production';
  process.env.NODE_ENV_TYPE = 'kkprod';
  
  const webpackConfig = require('../build/webpack.base.conf');
  console.log('   ✅ webpack.base.conf.js 加载成功');
  
  const prodConfig = require('../build/webpack.prod.conf');
  console.log('   ✅ webpack.prod.conf.js 加载成功');
  
  const config = require('../config');
  console.log('   ✅ config/index.js 加载成功');
  console.log(`   📦 Gzip压缩: ${config.build.productionGzip ? '启用' : '禁用'}`);
  
} catch (error) {
  console.log(`   ❌ Webpack配置加载失败: ${error.message}`);
}

console.log('\n🎉 配置测试完成！');
console.log('\n📝 使用说明:');
console.log('   开发环境: npm run dev');
console.log('   测试构建: npm run build:kktest 或 npm run build:ykytest');
console.log('   生产构建: npm run build:kkprod 或 npm run build:ykyprod');
console.log('   构建分析: npm run build:report');
