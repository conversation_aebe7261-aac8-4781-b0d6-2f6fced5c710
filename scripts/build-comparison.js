#!/usr/bin/env node

/**
 * 构建对比脚本
 * 比较不同环境构建结果的文件大小
 */

const fs = require('fs');
const path = require('path');

function getDirectorySize(dirPath) {
  if (!fs.existsSync(dirPath)) {
    return 0;
  }
  
  let totalSize = 0;
  const files = fs.readdirSync(dirPath);
  
  files.forEach(file => {
    const filePath = path.join(dirPath, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isDirectory()) {
      totalSize += getDirectorySize(filePath);
    } else {
      totalSize += stats.size;
    }
  });
  
  return totalSize;
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function getFilesByType(dirPath, extensions) {
  if (!fs.existsSync(dirPath)) {
    return [];
  }
  
  let files = [];
  const items = fs.readdirSync(dirPath);
  
  items.forEach(item => {
    const itemPath = path.join(dirPath, item);
    const stats = fs.statSync(itemPath);
    
    if (stats.isDirectory()) {
      files = files.concat(getFilesByType(itemPath, extensions));
    } else {
      const ext = path.extname(item).toLowerCase();
      if (extensions.includes(ext)) {
        files.push({
          path: itemPath,
          size: stats.size,
          name: item
        });
      }
    }
  });
  
  return files;
}

function analyzeBuildResult(distPath, envName) {
  console.log(`\n📊 ${envName} 构建结果分析:`);
  console.log(`📁 目录: ${distPath}`);
  
  if (!fs.existsSync(distPath)) {
    console.log('   ❌ 构建目录不存在，请先执行构建命令');
    return null;
  }
  
  const totalSize = getDirectorySize(distPath);
  console.log(`📦 总大小: ${formatBytes(totalSize)}`);
  
  // 分析不同类型文件
  const jsFiles = getFilesByType(distPath, ['.js']);
  const cssFiles = getFilesByType(distPath, ['.css']);
  const imgFiles = getFilesByType(distPath, ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.svg']);
  const gzFiles = getFilesByType(distPath, ['.gz']);
  
  const jsSize = jsFiles.reduce((sum, file) => sum + file.size, 0);
  const cssSize = cssFiles.reduce((sum, file) => sum + file.size, 0);
  const imgSize = imgFiles.reduce((sum, file) => sum + file.size, 0);
  const gzSize = gzFiles.reduce((sum, file) => sum + file.size, 0);
  
  console.log(`   🟨 JavaScript: ${formatBytes(jsSize)} (${jsFiles.length} 文件)`);
  console.log(`   🟦 CSS: ${formatBytes(cssSize)} (${cssFiles.length} 文件)`);
  console.log(`   🟩 图片: ${formatBytes(imgSize)} (${imgFiles.length} 文件)`);
  if (gzSize > 0) {
    console.log(`   🗜️  Gzip: ${formatBytes(gzSize)} (${gzFiles.length} 文件)`);
  }
  
  // 显示最大的几个文件
  const allFiles = [...jsFiles, ...cssFiles, ...imgFiles]
    .sort((a, b) => b.size - a.size)
    .slice(0, 5);
  
  if (allFiles.length > 0) {
    console.log('   📋 最大文件:');
    allFiles.forEach((file, index) => {
      const relativePath = path.relative(distPath, file.path);
      console.log(`      ${index + 1}. ${relativePath} - ${formatBytes(file.size)}`);
    });
  }
  
  return {
    total: totalSize,
    js: jsSize,
    css: cssSize,
    img: imgSize,
    gz: gzSize,
    files: {
      js: jsFiles.length,
      css: cssFiles.length,
      img: imgFiles.length,
      gz: gzFiles.length
    }
  };
}

console.log('🔍 构建结果对比分析');
console.log('=' .repeat(50));

// 分析各个环境的构建结果
const environments = [
  { name: 'KK测试环境', path: 'distTest' },
  { name: 'YKY测试环境', path: 'distYkyTest' },
  { name: 'KK生产环境', path: 'dist' },
  { name: 'YKY生产环境', path: 'distYky' }
];

const results = [];

environments.forEach(env => {
  const result = analyzeBuildResult(env.path, env.name);
  if (result) {
    results.push({ name: env.name, ...result });
  }
});

// 对比分析
if (results.length > 1) {
  console.log('\n📈 环境对比:');
  console.log('=' .repeat(50));
  
  const baseline = results[0];
  results.slice(1).forEach(result => {
    console.log(`\n🔄 ${result.name} vs ${baseline.name}:`);
    
    const totalDiff = ((result.total - baseline.total) / baseline.total * 100).toFixed(1);
    const jsDiff = baseline.js > 0 ? ((result.js - baseline.js) / baseline.js * 100).toFixed(1) : 'N/A';
    const cssDiff = baseline.css > 0 ? ((result.css - baseline.css) / baseline.css * 100).toFixed(1) : 'N/A';
    const imgDiff = baseline.img > 0 ? ((result.img - baseline.img) / baseline.img * 100).toFixed(1) : 'N/A';
    
    console.log(`   📦 总大小: ${totalDiff > 0 ? '+' : ''}${totalDiff}%`);
    console.log(`   🟨 JavaScript: ${jsDiff !== 'N/A' ? (jsDiff > 0 ? '+' : '') + jsDiff + '%' : jsDiff}`);
    console.log(`   🟦 CSS: ${cssDiff !== 'N/A' ? (cssDiff > 0 ? '+' : '') + cssDiff + '%' : cssDiff}`);
    console.log(`   🟩 图片: ${imgDiff !== 'N/A' ? (imgDiff > 0 ? '+' : '') + imgDiff + '%' : imgDiff}`);
  });
}

console.log('\n💡 提示:');
console.log('   • 生产环境应该比测试环境文件更小');
console.log('   • 如果某个环境构建目录不存在，请先运行对应的构建命令');
console.log('   • 使用 npm run build:report 可以生成详细的打包分析报告');
