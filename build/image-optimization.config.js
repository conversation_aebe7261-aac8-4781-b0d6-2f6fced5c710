'use strict';

/**
 * 图片压缩优化配置
 * 根据不同环境配置不同的压缩参数
 */

const getImageOptimizationConfig = () => {
  const envType = process.env.NODE_ENV_TYPE;
  const isProduction = process.env.NODE_ENV === 'production';
  
  // 生产环境配置（线上包）
  const prodConfig = {
    mozjpeg: {
      progressive: true,
      quality: 50, // 线上包更激进的压缩
    },
    optipng: {
      enabled: true,
    },
    pngquant: {
      quality: [0.5, 0.65], // 线上包更激进的压缩
      speed: 4,
    },
    gifsicle: {
      interlaced: false,
      optimizationLevel: 3,
    },
    webp: {
      quality: 75, // 线上包更激进的压缩
    },
  };

  // 测试环境配置（测试包）
  const testConfig = {
    mozjpeg: {
      progressive: true,
      quality: 65, // 测试包稍微宽松的压缩
    },
    optipng: {
      enabled: true,
    },
    pngquant: {
      quality: [0.65, 0.8], // 测试包稍微宽松的压缩
      speed: 4,
    },
    gifsicle: {
      interlaced: false,
      optimizationLevel: 2,
    },
    webp: {
      quality: 85, // 测试包稍微宽松的压缩
    },
  };

  // 开发环境配置
  const devConfig = {
    mozjpeg: {
      progressive: true,
      quality: 85, // 开发环境保持较高质量
    },
    optipng: {
      enabled: false, // 开发环境关闭PNG优化以提高构建速度
    },
    pngquant: {
      quality: [0.8, 0.9],
      speed: 1, // 开发环境使用最快速度
    },
    gifsicle: {
      interlaced: false,
      optimizationLevel: 1,
    },
    webp: {
      quality: 90,
    },
  };

  // 根据环境返回对应配置
  if (!isProduction) {
    return devConfig;
  }

  if (envType && envType.includes('prod')) {
    console.log('🖼️  使用生产环境图片压缩配置 (高压缩率)');
    return prodConfig;
  } else {
    console.log('🖼️  使用测试环境图片压缩配置 (中等压缩率)');
    return testConfig;
  }
};

module.exports = {
  getImageOptimizationConfig,
};
