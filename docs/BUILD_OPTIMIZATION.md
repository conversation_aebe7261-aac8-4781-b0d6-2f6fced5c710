# 构建优化配置说明

## 概述
本项目已配置了完整的构建优化方案，包括图片压缩、代码压缩、Gzip压缩等，并根据不同环境（生产包和测试包）设置了不同的优化参数。

## 环境配置
项目支持4种构建环境：
- `kkprod` - KK生产环境
- `ykyprod` - YKY生产环境  
- `kktest` - KK测试环境
- `ykytest` - YKY测试环境

## 构建命令
```bash
# 生产环境构建
npm run build:kkprod   # KK生产包
npm run build:ykyprod  # YKY生产包

# 测试环境构建
npm run build:kktest   # KK测试包
npm run build:ykytest  # YKY测试包
```

## 优化配置详情

### 1. 图片压缩优化
**配置文件**: `build/image-optimization.config.js`

#### 生产环境 (prod)
- **JPEG压缩**: quality: 50 (高压缩率)
- **PNG压缩**: quality: [0.5, 0.65] (高压缩率)
- **WebP压缩**: quality: 75 (高压缩率)
- **GIF压缩**: optimizationLevel: 3

#### 测试环境 (test)
- **JPEG压缩**: quality: 65 (中等压缩率)
- **PNG压缩**: quality: [0.65, 0.8] (中等压缩率)
- **WebP压缩**: quality: 85 (中等压缩率)
- **GIF压缩**: optimizationLevel: 2

#### 开发环境 (dev)
- **JPEG压缩**: quality: 85 (保持高质量)
- **PNG压缩**: 关闭优化 (提高构建速度)
- **WebP压缩**: quality: 90 (保持高质量)
- **GIF压缩**: optimizationLevel: 1

#### 其他图片优化
- **Base64转换**: 12KB以下图片自动转为base64
- **文件命名**: 使用2位hash值 `[name].[hash:2].[ext]`
- **支持格式**: PNG, JPEG, GIF, WebP

### 2. JavaScript压缩优化
**配置文件**: `build/webpack.prod.conf.js`

#### 生产环境优化
- **Console移除**: 完全移除 console.log, console.info, console.warn
- **压缩级别**: 2次压缩 (passes: 2)
- **不安全优化**: 启用 unsafe 和 unsafe_comps
- **Source Map**: 根据配置决定是否生成

#### 测试环境优化
- **Console移除**: 仅移除 console.log
- **压缩级别**: 1次压缩 (passes: 1)
- **不安全优化**: 关闭
- **Source Map**: 根据配置决定是否生成

### 3. CSS压缩优化
- **移除注释**: 完全移除CSS注释
- **安全压缩**: 启用safe模式
- **禁用autoprefixer**: 避免重复处理
- **合并属性**: 关闭mergeLonghand避免兼容性问题

### 4. Gzip压缩
**启用条件**: 生产环境和测试环境自动启用

#### 生产环境
- **压缩阈值**: 8KB以上文件进行压缩
- **压缩比**: 0.7 (更激进的压缩)
- **支持格式**: js, css, html, svg

#### 测试环境
- **压缩阈值**: 10KB以上文件进行压缩
- **压缩比**: 0.8 (标准压缩)
- **支持格式**: js, css, html, svg

### 5. 代码分割优化
- **第三方库**: 单独打包为 chunk-libs
- **Element UI**: 单独打包为 chunk-elementUI
- **IM相关**: 单独打包为 chunk-yxim (包含 yxim, xkit-yx, nertc-web-sdk)
- **Runtime**: 单独提取为 runtime chunk

## 构建结果对比

### 文件大小优化预期
- **图片文件**: 生产环境可减少30-50%，测试环境可减少20-35%
- **JavaScript**: 生产环境可减少15-25%，测试环境可减少10-20%
- **CSS文件**: 可减少10-20%
- **Gzip后**: 整体可再减少60-80%

### 构建时间
- **开发环境**: 图片优化最小化，构建速度最快
- **测试环境**: 中等优化，构建时间适中
- **生产环境**: 最大优化，构建时间较长但文件最小

## 使用建议

1. **开发阶段**: 使用 `npm run dev`，图片优化最小化，构建速度快
2. **测试部署**: 使用对应的test命令，平衡文件大小和构建速度
3. **生产部署**: 使用对应的prod命令，获得最小的文件大小
4. **性能分析**: 使用 `npm run build:report` 分析打包结果

## 注意事项

1. **首次构建**: 由于需要处理图片压缩，首次构建时间会较长
2. **缓存机制**: image-webpack-loader支持缓存，后续构建会更快
3. **图片格式**: 建议使用WebP格式获得更好的压缩效果
4. **兼容性**: 所有优化配置都考虑了浏览器兼容性

## 监控和调试

构建过程中会输出优化信息：
- 图片压缩配置信息
- Gzip压缩参数
- 文件大小对比

如需调试，可以：
1. 查看构建日志中的优化信息
2. 使用 `--report` 参数生成分析报告
3. 检查dist目录中的文件大小变化
